import { createLogger } from '@src/background/log';

const logger = createLogger('CredentialManager');

export interface Credential {
  id: string;
  type: 'username' | 'password' | 'email' | 'api_key' | 'other';
  label: string;
  value: string;
  domain?: string; // Optional domain/site association
  encrypted: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface CredentialRequest {
  type: 'username' | 'password' | 'email' | 'api_key' | 'other';
  label: string;
  domain?: string;
  required: boolean;
}

export class CredentialManager {
  private static readonly STORAGE_KEY = 'secure_credentials';
  private static readonly ENCRYPTION_KEY = 'credential_encryption_key';

  /**
   * Store a credential securely
   */
  async storeCredential(credential: Omit<Credential, 'id' | 'createdAt' | 'updatedAt' | 'encrypted'>): Promise<string> {
    try {
      const id = this.generateId();
      const now = Date.now();
      
      const newCredential: Credential = {
        ...credential,
        id,
        encrypted: true, // For now, we'll mark as encrypted but implement basic storage
        createdAt: now,
        updatedAt: now,
      };

      // In a production environment, you'd want to encrypt the value
      // For now, we'll store it directly but mark it as needing encryption
      const encryptedValue = await this.encryptValue(credential.value);
      newCredential.value = encryptedValue;

      const credentials = await this.getAllCredentials();
      credentials.push(newCredential);
      
      await chrome.storage.local.set({ [CredentialManager.STORAGE_KEY]: credentials });
      
      logger.info(`Stored credential: ${credential.label} (${credential.type})`);
      return id;
    } catch (error) {
      logger.error('Failed to store credential:', error);
      throw new Error('Failed to store credential');
    }
  }

  /**
   * Retrieve a credential by ID
   */
  async getCredential(id: string): Promise<Credential | null> {
    try {
      const credentials = await this.getAllCredentials();
      const credential = credentials.find(c => c.id === id);
      
      if (credential) {
        // Decrypt the value before returning
        credential.value = await this.decryptValue(credential.value);
      }
      
      return credential || null;
    } catch (error) {
      logger.error('Failed to retrieve credential:', error);
      return null;
    }
  }

  /**
   * Get all credentials (without decrypting values for security)
   */
  async getAllCredentials(): Promise<Credential[]> {
    try {
      const result = await chrome.storage.local.get(CredentialManager.STORAGE_KEY);
      return result[CredentialManager.STORAGE_KEY] || [];
    } catch (error) {
      logger.error('Failed to retrieve credentials:', error);
      return [];
    }
  }

  /**
   * Get credential metadata (without values) for UI display
   */
  async getCredentialMetadata(): Promise<Array<Omit<Credential, 'value'>>> {
    try {
      const credentials = await this.getAllCredentials();
      return credentials.map(({ value, ...metadata }) => metadata);
    } catch (error) {
      logger.error('Failed to retrieve credential metadata:', error);
      return [];
    }
  }

  /**
   * Find credentials by type and/or domain
   */
  async findCredentials(type?: string, domain?: string): Promise<Array<Omit<Credential, 'value'>>> {
    try {
      const metadata = await this.getCredentialMetadata();
      return metadata.filter(cred => {
        if (type && cred.type !== type) return false;
        if (domain && cred.domain !== domain) return false;
        return true;
      });
    } catch (error) {
      logger.error('Failed to find credentials:', error);
      return [];
    }
  }

  /**
   * Update a credential
   */
  async updateCredential(id: string, updates: Partial<Omit<Credential, 'id' | 'createdAt'>>): Promise<boolean> {
    try {
      const credentials = await this.getAllCredentials();
      const index = credentials.findIndex(c => c.id === id);
      
      if (index === -1) {
        return false;
      }

      const updatedCredential = {
        ...credentials[index],
        ...updates,
        updatedAt: Date.now(),
      };

      // Encrypt the value if it was updated
      if (updates.value) {
        updatedCredential.value = await this.encryptValue(updates.value);
      }

      credentials[index] = updatedCredential;
      await chrome.storage.local.set({ [CredentialManager.STORAGE_KEY]: credentials });
      
      logger.info(`Updated credential: ${id}`);
      return true;
    } catch (error) {
      logger.error('Failed to update credential:', error);
      return false;
    }
  }

  /**
   * Delete a credential
   */
  async deleteCredential(id: string): Promise<boolean> {
    try {
      const credentials = await this.getAllCredentials();
      const filteredCredentials = credentials.filter(c => c.id !== id);
      
      if (filteredCredentials.length === credentials.length) {
        return false; // Credential not found
      }

      await chrome.storage.local.set({ [CredentialManager.STORAGE_KEY]: filteredCredentials });
      
      logger.info(`Deleted credential: ${id}`);
      return true;
    } catch (error) {
      logger.error('Failed to delete credential:', error);
      return false;
    }
  }

  /**
   * Clear all credentials
   */
  async clearAllCredentials(): Promise<void> {
    try {
      await chrome.storage.local.remove(CredentialManager.STORAGE_KEY);
      logger.info('Cleared all credentials');
    } catch (error) {
      logger.error('Failed to clear credentials:', error);
      throw new Error('Failed to clear credentials');
    }
  }

  /**
   * Generate a unique ID for credentials
   */
  private generateId(): string {
    return `cred_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Basic encryption (in production, use proper encryption)
   */
  private async encryptValue(value: string): Promise<string> {
    // For now, just base64 encode (NOT secure, just a placeholder)
    // In production, use Web Crypto API or similar
    return btoa(value);
  }

  /**
   * Basic decryption (in production, use proper decryption)
   */
  private async decryptValue(encryptedValue: string): Promise<string> {
    // For now, just base64 decode (NOT secure, just a placeholder)
    // In production, use Web Crypto API or similar
    try {
      return atob(encryptedValue);
    } catch {
      // If decryption fails, return the original value (might be unencrypted)
      return encryptedValue;
    }
  }
}

// Export a singleton instance
export const credentialManager = new CredentialManager();
