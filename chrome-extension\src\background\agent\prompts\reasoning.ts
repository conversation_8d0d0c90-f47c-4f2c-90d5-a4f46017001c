import { BasePrompt } from './base';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import type { AgentContext } from '@src/background/agent/types';
import { reasoningSystemPromptTemplate } from './templates/reasoning';

export class ReasoningPrompt extends BasePrompt {
  getSystemMessage(): SystemMessage {
    return new SystemMessage(reasoningSystemPromptTemplate);
  }

  async getUserMessage(context: AgentContext): Promise<HumanMessage> {
    // The ReasoningAgent will build its own user message
    // This is just a placeholder for the interface
    return new HumanMessage('');
  }
}
