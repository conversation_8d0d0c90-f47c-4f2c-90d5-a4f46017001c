import { createLogger } from '@src/background/log';

const logger = createLogger('KnowledgeBase');

export interface WorkflowPattern {
  id: string;
  name: string;
  description: string;
  domain: string;
  keywords: string[];
  steps: WorkflowStep[];
  commonVariations: string[];
  requiredCredentials: string[];
  commonUrls: string[];
}

export interface WorkflowStep {
  action: string;
  description: string;
  selectors?: string[];
  expectedElements?: string[];
  alternatives?: string[];
}

export class KnowledgeBase {
  private workflows: WorkflowPattern[] = [];

  constructor() {
    this.initializeWorkflows();
  }

  /**
   * Initialize the knowledge base with common workflow patterns
   */
  private initializeWorkflows(): void {
    this.workflows = [
      // Acumatica Web Service Testing
      {
        id: 'acumatica_webservice_test',
        name: 'Acumatica Web Service Endpoint Testing',
        description: 'Test web service endpoints in Acumatica ERP system',
        domain: 'acumatica',
        keywords: ['acumatica', 'web service', 'endpoint', 'test', 'api', 'integration'],
        steps: [
          {
            action: 'navigate_to_login',
            description: 'Navigate to Acumatica login page',
            selectors: ['input[type="text"]', 'input[name="username"]', '#username'],
            expectedElements: ['login form', 'username field', 'password field']
          },
          {
            action: 'login',
            description: 'Enter credentials and log in',
            selectors: ['input[type="password"]', 'button[type="submit"]', '.login-button'],
            expectedElements: ['password field', 'login button']
          },
          {
            action: 'navigate_to_integration',
            description: 'Navigate to Integration module',
            selectors: ['[data-menu="Integration"]', 'a[href*="integration"]'],
            expectedElements: ['Integration menu item']
          },
          {
            action: 'open_web_service_endpoints',
            description: 'Open Web Service Endpoints screen',
            selectors: ['[data-screen="CS207500"]', 'a[href*="CS207500"]'],
            expectedElements: ['Web Service Endpoints screen']
          },
          {
            action: 'select_test_endpoint',
            description: 'Click on Test Endpoint button',
            selectors: ['button[data-cmd="TestEndpoint"]', '.test-endpoint-btn'],
            expectedElements: ['Test Endpoint button']
          },
          {
            action: 'configure_endpoint',
            description: 'Select and configure the specific endpoint to test',
            selectors: ['select[data-field="EndpointName"]', '.endpoint-selector'],
            expectedElements: ['endpoint dropdown', 'configuration options']
          },
          {
            action: 'execute_test',
            description: 'Execute the endpoint test',
            selectors: ['button[data-cmd="Execute"]', '.execute-btn'],
            expectedElements: ['Execute button', 'test results area']
          }
        ],
        commonVariations: [
          'test shipment endpoint',
          'test customer endpoint', 
          'test invoice endpoint',
          'test item endpoint',
          'test vendor endpoint'
        ],
        requiredCredentials: ['username', 'password'],
        commonUrls: [
          'https://*.acumatica.com',
          'https://*/AcumaticaERP',
          'https://*/Acumatica'
        ]
      },

      // Generic Login Workflow
      {
        id: 'generic_login',
        name: 'Generic Website Login',
        description: 'Standard login process for web applications',
        domain: 'general',
        keywords: ['login', 'sign in', 'authenticate', 'credentials'],
        steps: [
          {
            action: 'find_login_form',
            description: 'Locate the login form on the page',
            selectors: ['form[name="login"]', '.login-form', '#login-form'],
            expectedElements: ['login form', 'username field', 'password field']
          },
          {
            action: 'enter_username',
            description: 'Enter username or email',
            selectors: ['input[name="username"]', 'input[name="email"]', 'input[type="email"]'],
            expectedElements: ['username input field']
          },
          {
            action: 'enter_password',
            description: 'Enter password',
            selectors: ['input[name="password"]', 'input[type="password"]'],
            expectedElements: ['password input field']
          },
          {
            action: 'submit_login',
            description: 'Submit the login form',
            selectors: ['button[type="submit"]', 'input[type="submit"]', '.login-btn'],
            expectedElements: ['submit button']
          },
          {
            action: 'verify_login',
            description: 'Verify successful login',
            selectors: ['.user-menu', '.logout-btn', '.dashboard'],
            expectedElements: ['user menu', 'dashboard', 'logout option']
          }
        ],
        commonVariations: [
          'log into the system',
          'sign in to account',
          'authenticate user',
          'access dashboard'
        ],
        requiredCredentials: ['username', 'password'],
        commonUrls: []
      },

      // E-commerce Order Management
      {
        id: 'ecommerce_order_check',
        name: 'E-commerce Order Management',
        description: 'Check and manage orders in e-commerce systems',
        domain: 'ecommerce',
        keywords: ['orders', 'order history', 'purchase', 'shopping', 'account'],
        steps: [
          {
            action: 'navigate_to_account',
            description: 'Navigate to user account section',
            selectors: ['.account-menu', '.user-menu', 'a[href*="account"]'],
            expectedElements: ['account menu', 'user profile']
          },
          {
            action: 'access_orders',
            description: 'Access order history or order management',
            selectors: ['a[href*="orders"]', '.order-history', '.my-orders'],
            expectedElements: ['orders section', 'order list']
          },
          {
            action: 'filter_orders',
            description: 'Filter or search for specific orders',
            selectors: ['.order-filter', 'input[name="search"]', '.date-filter'],
            expectedElements: ['filter options', 'search field']
          },
          {
            action: 'view_order_details',
            description: 'View detailed information about orders',
            selectors: ['.order-details', '.view-order', 'a[href*="order"]'],
            expectedElements: ['order details', 'order items', 'order status']
          }
        ],
        commonVariations: [
          'check my orders',
          'view order history',
          'find recent purchases',
          'track order status'
        ],
        requiredCredentials: ['username', 'password'],
        commonUrls: []
      }
    ];

    logger.info(`Initialized knowledge base with ${this.workflows.length} workflow patterns`);
  }

  /**
   * Find matching workflow patterns based on user request
   */
  findMatchingWorkflows(userRequest: string, domain?: string): WorkflowPattern[] {
    const request = userRequest.toLowerCase();
    const matches: Array<{ workflow: WorkflowPattern; score: number }> = [];

    for (const workflow of this.workflows) {
      let score = 0;

      // Domain match bonus
      if (domain && workflow.domain === domain) {
        score += 10;
      }

      // Keyword matching
      for (const keyword of workflow.keywords) {
        if (request.includes(keyword.toLowerCase())) {
          score += 5;
        }
      }

      // Variation matching
      for (const variation of workflow.commonVariations) {
        if (request.includes(variation.toLowerCase())) {
          score += 8;
        }
      }

      // Name and description matching
      if (request.includes(workflow.name.toLowerCase())) {
        score += 15;
      }

      if (score > 0) {
        matches.push({ workflow, score });
      }
    }

    // Sort by score and return workflows
    return matches
      .sort((a, b) => b.score - a.score)
      .map(match => match.workflow);
  }

  /**
   * Get workflow by ID
   */
  getWorkflow(id: string): WorkflowPattern | null {
    return this.workflows.find(w => w.id === id) || null;
  }

  /**
   * Get all workflows for a specific domain
   */
  getWorkflowsByDomain(domain: string): WorkflowPattern[] {
    return this.workflows.filter(w => w.domain === domain);
  }

  /**
   * Infer missing steps based on workflow patterns
   */
  inferMissingSteps(userRequest: string, currentSteps: string[]): string[] {
    const matchingWorkflows = this.findMatchingWorkflows(userRequest);
    
    if (matchingWorkflows.length === 0) {
      return [];
    }

    const bestMatch = matchingWorkflows[0];
    const inferredSteps: string[] = [];

    // Check which steps from the pattern are missing
    for (const step of bestMatch.steps) {
      const stepExists = currentSteps.some(currentStep => 
        currentStep.toLowerCase().includes(step.action.toLowerCase()) ||
        currentStep.toLowerCase().includes(step.description.toLowerCase())
      );

      if (!stepExists) {
        inferredSteps.push(step.description);
      }
    }

    return inferredSteps;
  }

  /**
   * Get required credentials for a workflow
   */
  getRequiredCredentials(userRequest: string): string[] {
    const matchingWorkflows = this.findMatchingWorkflows(userRequest);
    
    if (matchingWorkflows.length === 0) {
      return [];
    }

    const allCredentials = new Set<string>();
    for (const workflow of matchingWorkflows.slice(0, 3)) { // Top 3 matches
      workflow.requiredCredentials.forEach(cred => allCredentials.add(cred));
    }

    return Array.from(allCredentials);
  }

  /**
   * Detect domain from user request or URL
   */
  detectDomain(userRequest: string, currentUrl?: string): string {
    const request = userRequest.toLowerCase();

    // Check for specific domain keywords
    if (request.includes('acumatica')) return 'acumatica';
    if (request.includes('order') || request.includes('purchase') || request.includes('shopping')) return 'ecommerce';
    
    // Check URL patterns
    if (currentUrl) {
      const url = currentUrl.toLowerCase();
      if (url.includes('acumatica')) return 'acumatica';
      if (url.includes('shop') || url.includes('store') || url.includes('cart')) return 'ecommerce';
    }

    return 'general';
  }

  /**
   * Get contextual suggestions based on current state
   */
  getContextualSuggestions(userRequest: string, currentUrl?: string): {
    suggestedSteps: string[];
    requiredCredentials: string[];
    detectedDomain: string;
    matchingWorkflows: WorkflowPattern[];
  } {
    const detectedDomain = this.detectDomain(userRequest, currentUrl);
    const matchingWorkflows = this.findMatchingWorkflows(userRequest, detectedDomain);
    const requiredCredentials = this.getRequiredCredentials(userRequest);
    
    let suggestedSteps: string[] = [];
    if (matchingWorkflows.length > 0) {
      suggestedSteps = matchingWorkflows[0].steps.map(step => step.description);
    }

    return {
      suggestedSteps,
      requiredCredentials,
      detectedDomain,
      matchingWorkflows: matchingWorkflows.slice(0, 3) // Return top 3 matches
    };
  }
}

// Export singleton instance
export const knowledgeBase = new KnowledgeBase();
