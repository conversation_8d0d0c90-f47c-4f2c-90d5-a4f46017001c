import { createLogger } from '@src/background/log';
import { knowledgeBase } from './knowledgeBase';

const logger = createLogger('ErrorRecovery');

export interface ErrorContext {
  errorType: string;
  errorMessage: string;
  failedAction: string;
  currentUrl: string;
  attemptNumber: number;
  maxAttempts: number;
  userRequest: string;
  previousActions: string[];
}

export interface RecoveryStrategy {
  id: string;
  name: string;
  description: string;
  applicableErrors: string[];
  priority: number;
  execute: (context: ErrorContext) => Promise<RecoveryAction[]>;
}

export interface RecoveryAction {
  type: 'retry' | 'alternative_approach' | 'ask_user' | 'skip_step' | 'restart_workflow';
  description: string;
  parameters?: Record<string, any>;
  reasoning: string;
}

export class ErrorRecoveryService {
  private strategies: RecoveryStrategy[] = [];

  constructor() {
    this.initializeStrategies();
  }

  /**
   * Initialize recovery strategies
   */
  private initializeStrategies(): void {
    this.strategies = [
      // Element not found recovery
      {
        id: 'element_not_found',
        name: 'Element Not Found Recovery',
        description: 'Handle cases where expected elements cannot be found',
        applicableErrors: ['element_not_found', 'selector_failed', 'timeout'],
        priority: 1,
        execute: async (context: ErrorContext): Promise<RecoveryAction[]> => {
          const actions: RecoveryAction[] = [];

          // Try alternative selectors
          actions.push({
            type: 'alternative_approach',
            description: 'Try alternative element selectors',
            parameters: { useAlternativeSelectors: true },
            reasoning: 'The original selector may be outdated or the page structure changed'
          });

          // Wait and retry
          if (context.attemptNumber < 2) {
            actions.push({
              type: 'retry',
              description: 'Wait for page to load completely and retry',
              parameters: { waitTime: 3000 },
              reasoning: 'Page may still be loading or elements may appear dynamically'
            });
          }

          // Ask user for guidance
          actions.push({
            type: 'ask_user',
            description: 'Ask user to identify the correct element or provide guidance',
            parameters: { 
              question: `I couldn't find the expected element for "${context.failedAction}". Can you help me locate it or suggest an alternative approach?`,
              options: ['Try different approach', 'Skip this step', 'Restart workflow']
            },
            reasoning: 'User input can help identify changes in the interface or provide alternative paths'
          });

          return actions;
        }
      },

      // Authentication failure recovery
      {
        id: 'auth_failure',
        name: 'Authentication Failure Recovery',
        description: 'Handle login and authentication failures',
        applicableErrors: ['login_failed', 'auth_error', 'session_expired'],
        priority: 1,
        execute: async (context: ErrorContext): Promise<RecoveryAction[]> => {
          const actions: RecoveryAction[] = [];

          // Check if credentials are available
          actions.push({
            type: 'ask_user',
            description: 'Request updated credentials',
            parameters: {
              question: 'Authentication failed. Please provide updated login credentials.',
              credentialsNeeded: ['username', 'password']
            },
            reasoning: 'Credentials may be incorrect, expired, or the user may need to provide them'
          });

          // Try alternative login methods
          actions.push({
            type: 'alternative_approach',
            description: 'Try alternative login methods (SSO, different login page)',
            parameters: { exploreAlternativeAuth: true },
            reasoning: 'The site may have multiple authentication options'
          });

          return actions;
        }
      },

      // Network/connectivity issues
      {
        id: 'network_error',
        name: 'Network Error Recovery',
        description: 'Handle network connectivity and loading issues',
        applicableErrors: ['network_error', 'page_load_failed', 'timeout'],
        priority: 2,
        execute: async (context: ErrorContext): Promise<RecoveryAction[]> => {
          const actions: RecoveryAction[] = [];

          // Retry with backoff
          if (context.attemptNumber < 3) {
            actions.push({
              type: 'retry',
              description: 'Retry after waiting for network recovery',
              parameters: { waitTime: 5000 * context.attemptNumber },
              reasoning: 'Network issues may be temporary and resolve with time'
            });
          }

          // Refresh page and restart
          actions.push({
            type: 'alternative_approach',
            description: 'Refresh the page and restart the current step',
            parameters: { refreshPage: true },
            reasoning: 'Page refresh may resolve loading issues or stale content'
          });

          return actions;
        }
      },

      // Workflow context recovery
      {
        id: 'workflow_context',
        name: 'Workflow Context Recovery',
        description: 'Recover when the workflow gets off track',
        applicableErrors: ['unexpected_page', 'wrong_context', 'navigation_failed'],
        priority: 1,
        execute: async (context: ErrorContext): Promise<RecoveryAction[]> => {
          const actions: RecoveryAction[] = [];

          // Use knowledge base to find alternative paths
          const contextualInfo = knowledgeBase.getContextualSuggestions(context.userRequest, context.currentUrl);
          
          if (contextualInfo.matchingWorkflows.length > 0) {
            actions.push({
              type: 'alternative_approach',
              description: 'Use alternative workflow path based on knowledge base',
              parameters: { 
                alternativeWorkflow: contextualInfo.matchingWorkflows[0],
                suggestedSteps: contextualInfo.suggestedSteps
              },
              reasoning: 'Knowledge base contains alternative approaches for this type of task'
            });
          }

          // Navigate back to known state
          actions.push({
            type: 'alternative_approach',
            description: 'Navigate back to a known starting point',
            parameters: { navigateToHome: true },
            reasoning: 'Starting from a known state can help re-establish the correct workflow context'
          });

          // Ask user for guidance
          actions.push({
            type: 'ask_user',
            description: 'Ask user for guidance on how to proceed',
            parameters: {
              question: `I seem to have gotten off track. The current page doesn't match what I expected for "${context.failedAction}". How would you like me to proceed?`,
              options: ['Start over', 'Try different approach', 'Manual guidance']
            },
            reasoning: 'User can provide context about the current state and preferred approach'
          });

          return actions;
        }
      },

      // Generic fallback recovery
      {
        id: 'generic_fallback',
        name: 'Generic Fallback Recovery',
        description: 'General recovery strategies for unhandled errors',
        applicableErrors: ['*'], // Matches any error
        priority: 10, // Lowest priority
        execute: async (context: ErrorContext): Promise<RecoveryAction[]> => {
          const actions: RecoveryAction[] = [];

          // Simple retry if not attempted too many times
          if (context.attemptNumber < 2) {
            actions.push({
              type: 'retry',
              description: 'Simple retry of the failed action',
              parameters: { waitTime: 2000 },
              reasoning: 'Some errors may be transient and resolve with a simple retry'
            });
          }

          // Skip the problematic step
          actions.push({
            type: 'skip_step',
            description: 'Skip this step and continue with the workflow',
            parameters: {},
            reasoning: 'This step may not be critical to the overall goal'
          });

          // Ask user for help
          actions.push({
            type: 'ask_user',
            description: 'Ask user for guidance on how to handle this error',
            parameters: {
              question: `I encountered an error: "${context.errorMessage}". How would you like me to proceed?`,
              options: ['Retry', 'Skip this step', 'Try different approach', 'Stop execution']
            },
            reasoning: 'User input is needed to determine the best course of action'
          });

          return actions;
        }
      }
    ];

    logger.info(`Initialized error recovery service with ${this.strategies.length} strategies`);
  }

  /**
   * Analyze an error and suggest recovery actions
   */
  async analyzeError(context: ErrorContext): Promise<RecoveryAction[]> {
    logger.info(`Analyzing error: ${context.errorType} - ${context.errorMessage}`);

    const applicableStrategies = this.strategies
      .filter(strategy => 
        strategy.applicableErrors.includes(context.errorType) || 
        strategy.applicableErrors.includes('*')
      )
      .sort((a, b) => a.priority - b.priority);

    const allActions: RecoveryAction[] = [];

    for (const strategy of applicableStrategies) {
      try {
        const actions = await strategy.execute(context);
        allActions.push(...actions);
      } catch (error) {
        logger.error(`Error executing recovery strategy ${strategy.id}:`, error);
      }
    }

    // Remove duplicates and prioritize actions
    const uniqueActions = this.deduplicateActions(allActions);
    
    logger.info(`Generated ${uniqueActions.length} recovery actions for error: ${context.errorType}`);
    return uniqueActions;
  }

  /**
   * Remove duplicate recovery actions
   */
  private deduplicateActions(actions: RecoveryAction[]): RecoveryAction[] {
    const seen = new Set<string>();
    return actions.filter(action => {
      const key = `${action.type}-${action.description}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Get strategy by ID
   */
  getStrategy(id: string): RecoveryStrategy | null {
    return this.strategies.find(s => s.id === id) || null;
  }

  /**
   * Add a custom recovery strategy
   */
  addStrategy(strategy: RecoveryStrategy): void {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => a.priority - b.priority);
    logger.info(`Added custom recovery strategy: ${strategy.id}`);
  }

  /**
   * Create error context from execution state
   */
  createErrorContext(
    errorType: string,
    errorMessage: string,
    failedAction: string,
    currentUrl: string,
    attemptNumber: number,
    userRequest: string,
    previousActions: string[] = []
  ): ErrorContext {
    return {
      errorType,
      errorMessage,
      failedAction,
      currentUrl,
      attemptNumber,
      maxAttempts: 3,
      userRequest,
      previousActions
    };
  }
}

// Export singleton instance
export const errorRecoveryService = new ErrorRecoveryService();
