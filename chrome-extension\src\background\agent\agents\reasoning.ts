import { z } from 'zod';
import { BaseAgent, type BaseAgentOptions, type ExtraAgentOptions } from './base';
import type { AgentOutput } from '../types';
import { HumanMessage } from '@langchain/core/messages';
import { createLogger } from '@src/background/log';
import { Actors, ExecutionState } from '../event/types';
import {
  ChatModelAuthError,
  ChatModelForbiddenError,
  RequestCancelledError,
  isAuthenticationError,
  isForbiddenError,
  isAbortedError
} from './errors';
import { LLM_FORBIDDEN_ERROR_MESSAGE } from './errors';
import { knowledgeBase } from '@src/background/services/knowledgeBase';

const logger = createLogger('ReasoningAgent');

// Schema for reasoning agent output
export const reasoningOutputSchema = z.object({
  understanding: z.string().describe('Clear understanding of what the user wants to accomplish'),
  task_type: z.enum(['web_automation', 'information_request', 'complex_workflow', 'unclear']).describe('Type of task identified'),
  confidence: z.number().min(0).max(1).describe('Confidence level in understanding the request (0-1)'),
  missing_information: z.array(z.string()).describe('List of information needed to proceed'),
  clarifying_questions: z.array(z.string()).describe('Questions to ask the user for clarification'),
  inferred_steps: z.array(z.string()).describe('High-level steps inferred from the request'),
  context_needed: z.array(z.string()).describe('Additional context that would be helpful'),
  credentials_needed: z.array(z.object({
    type: z.enum(['username', 'password', 'email', 'api_key', 'other']),
    description: z.string(),
    required: z.boolean()
  })).describe('Authentication credentials that may be needed'),
  ready_to_proceed: z.boolean().describe('Whether we have enough information to start execution'),
  enhanced_task_description: z.string().describe('Enhanced, detailed task description for the planner'),
  reasoning: z.string().describe('Detailed reasoning about the task and approach')
});

export type ReasoningOutput = z.infer<typeof reasoningOutputSchema>;

export interface ReasoningContext {
  originalRequest: string;
  conversationHistory: string[];
  availableCredentials: string[];
  currentUrl?: string;
  userResponses: Record<string, string>;
}

export class ReasoningAgent extends BaseAgent<typeof reasoningOutputSchema, ReasoningOutput> {
  private reasoningContext: ReasoningContext;

  constructor(
    options: BaseAgentOptions,
    extraOptions?: Partial<ExtraAgentOptions>,
    initialContext?: Partial<ReasoningContext>
  ) {
    super(reasoningOutputSchema, options, { ...extraOptions, id: 'reasoning' });

    this.reasoningContext = {
      originalRequest: '',
      conversationHistory: [],
      availableCredentials: [],
      userResponses: {},
      ...initialContext
    };
  }

  /**
   * Update the reasoning context with new information
   */
  updateContext(updates: Partial<ReasoningContext>): void {
    this.reasoningContext = { ...this.reasoningContext, ...updates };
  }

  /**
   * Add a user response to a clarifying question
   */
  addUserResponse(question: string, response: string): void {
    this.reasoningContext.userResponses[question] = response;
    this.reasoningContext.conversationHistory.push(`Q: ${question}`);
    this.reasoningContext.conversationHistory.push(`A: ${response}`);
  }

  /**
   * Execute the reasoning process
   */
  async execute(userRequest?: string): Promise<AgentOutput<ReasoningOutput>> {
    try {
      this.context.emitEvent(Actors.REASONING, ExecutionState.STEP_START, 'Analyzing request...');
      
      if (userRequest) {
        this.reasoningContext.originalRequest = userRequest;
      }

      const systemMessage = this.prompt.getSystemMessage();
      const userMessage = await this.buildReasoningUserMessage();
      
      const inputMessages = [systemMessage, userMessage];
      
      const modelOutput = await this.invoke(inputMessages);
      if (!modelOutput) {
        throw new Error('Failed to generate reasoning output');
      }

      this.context.emitEvent(Actors.REASONING, ExecutionState.STEP_OK, 'Analysis complete');
      logger.info('Reasoning output', JSON.stringify(modelOutput, null, 2));

      return {
        id: this.id,
        result: modelOutput,
      };
    } catch (error) {
      // Handle authentication and other errors
      if (isAuthenticationError(error)) {
        throw new ChatModelAuthError('Reasoning API Authentication failed. Please verify your API key', error);
      }
      if (isForbiddenError(error)) {
        throw new ChatModelForbiddenError(LLM_FORBIDDEN_ERROR_MESSAGE, error);
      }
      if (isAbortedError(error)) {
        throw new RequestCancelledError((error as Error).message);
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Reasoning failed: ${errorMessage}`);
      this.context.emitEvent(Actors.REASONING, ExecutionState.STEP_FAIL, `Reasoning failed: ${errorMessage}`);
      
      return {
        id: this.id,
        error: `Reasoning failed: ${errorMessage}`,
      };
    }
  }

  /**
   * Build the user message for reasoning
   */
  private async buildReasoningUserMessage(): Promise<HumanMessage> {
    const { originalRequest, conversationHistory, availableCredentials, currentUrl, userResponses } = this.reasoningContext;

    let content = `Original user request: "${originalRequest}"\n\n`;

    if (currentUrl) {
      content += `Current page URL: ${currentUrl}\n\n`;
    }

    // Add contextual knowledge from knowledge base
    const contextualInfo = knowledgeBase.getContextualSuggestions(originalRequest, currentUrl);
    if (contextualInfo.matchingWorkflows.length > 0) {
      content += `Relevant workflow patterns found:\n`;
      for (const workflow of contextualInfo.matchingWorkflows) {
        content += `- ${workflow.name}: ${workflow.description}\n`;
        content += `  Typical steps: ${workflow.steps.map(s => s.description).join(' → ')}\n`;
      }
      content += '\n';
    }

    if (contextualInfo.detectedDomain !== 'general') {
      content += `Detected domain: ${contextualInfo.detectedDomain}\n\n`;
    }

    if (availableCredentials.length > 0) {
      content += `Available stored credentials: ${availableCredentials.join(', ')}\n\n`;
    }

    if (conversationHistory.length > 0) {
      content += `Previous conversation:\n${conversationHistory.join('\n')}\n\n`;
    }

    if (Object.keys(userResponses).length > 0) {
      content += `User responses to clarifying questions:\n`;
      for (const [question, response] of Object.entries(userResponses)) {
        content += `Q: ${question}\nA: ${response}\n`;
      }
      content += '\n';
    }

    content += `Please analyze this request and provide your reasoning about how to proceed.`;

    return new HumanMessage(content);
  }

  /**
   * Check if the agent is ready to proceed with execution
   */
  isReadyToProceed(): boolean {
    // This would be set by the last reasoning output
    return true; // Placeholder - would check the last reasoning result
  }

  /**
   * Get the enhanced task description for the planner
   */
  getEnhancedTaskDescription(): string {
    // This would return the enhanced task description from the last reasoning output
    return this.reasoningContext.originalRequest; // Placeholder
  }
}
